import requests
import os
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Replace with the URL of the page containing the files
url = "https://toddvdating.com/oda/welcome-to-online-dating-academy/"

# Replace with the cookies from your browser session
cookies = {
    "wordpress_logged_in_lalit.hilmarsh%40gmail.com%7C1750366908%7C7V5QzrsXo0hl2zXCzq59q5wXC1Wkx0A920CnlYDcaCU%7C51e2f1f9f8bd04ed5c9836f563abc366d5dbaa17151384b3b857623042b8e404": ""
}

# The directory to save the files in
download_dir = "/Volumes/Seagate Backup Plus Drive/game/Todd-V/infields"

def download_file(url, directory):
    local_filename = url.split('/')[-1]
    path = os.path.join(directory, local_filename)
    with requests.get(url, stream=True, cookies=cookies) as r:
        r.raise_for_status()
        with open(path, 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192):
                f.write(chunk)
    return local_filename

try:
    # Create the download directory if it doesn't exist
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)

    # Get the page content
    response = requests.get(url, cookies=cookies)
    response.raise_for_status()

    # Parse the HTML
    soup = BeautifulSoup(response.text, 'html.parser')

    # Find all links that point to video or audio files
    # This might need to be adjusted based on the actual HTML structure
    links = soup.find_all('a', href=True)
    download_links = []

    for link in links:
        href = link['href']
        if any(filetype in href for filetype in ['.mp4', '.mov', '.avi', '.mp3', '.wav', '.m4a']):
            download_links.append(urljoin(url, href))

    if not download_links:
        print("No download links found. You may need to adjust the script to find the correct links.")
    else:
        print(f"Found {len(download_links)} files to download.")
        for link in download_links:
            try:
                print(f"Downloading {link}...")
                filename = download_file(link, download_dir)
                print(f"Downloaded {filename}")
            except requests.exceptions.RequestException as e:
                print(f"Failed to download {link}: {e}")

except requests.exceptions.RequestException as e:
    print(f"Error accessing page: {e}")
except Exception as e:
    print(f"An error occurred: {e}")